import csv
import unidecode
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.metrics.pairwise import cosine_similarity
import numpy as np
from similaridade import sim_bert
import json

TRANSACTIONS_FILE = 'transactions.csv'
ACCOUNTS_FILE = 'account_202505281449_51889157000150_accounts.csv'
TOP_N = 1  # Mostrar apenas a melhor sugestão por transação


def sanitize(text):
    if not text:
        return ''
    text = unidecode.unidecode(str(text)).lower().strip()
    return ' '.join(text.split())


def load_transactions(path):
    with open(path, newline='', encoding='utf-8') as f:
        reader = csv.DictReader(f)
        return list(reader)


def load_accounts(path):
    with open(path, newline='', encoding='utf-8') as f:
        reader = csv.DictReader(f)
        # Considerar apenas contas analíticas e ativas
        return [row for row in reader if row.get('account_type', '').upper() == 'ANALYTIC' and row.get('status', '').upper() == 'ACTIVE']


def extrai_nome_completo(transacao):
    # Tenta extrair o nome completo do campo _metadata (JSON)
    meta = transacao.get('_metadata', '')
    if meta:
        try:
            meta_dict = json.loads(meta)
            # Procura em CUSTOMER ou SUPPLIER
            for entidade in ['CUSTOMER', 'SUPPLIER']:
                if 'originalApiData' in meta_dict and entidade in meta_dict['originalApiData']:
                    nome = meta_dict['originalApiData'][entidade].get('name')
                    if nome:
                        return nome
        except Exception:
            pass
    # Fallback para o campo name
    return transacao.get('name', '')


def main():
    transacoes = load_transactions(TRANSACTIONS_FILE)
    contas = load_accounts(ACCOUNTS_FILE)
    contas_desc = [sanitize(c['description']) for c in contas]
    consultas = [sanitize(f"{t.get('name', '')} {t.get('description', '')}") for t in transacoes]

    # TF-IDF
    vectorizer = TfidfVectorizer().fit(contas_desc)
    contas_matrix = vectorizer.transform(contas_desc)

    # BERT
    textos_bert = list(set([c for c in contas_desc + consultas if c]))
    sim_bert.prepara_bert_nomes(textos_bert)

    html = []
    html.append('<html><head><meta charset="utf-8"><title>Resultado Matching</title>')
    html.append('<style>body{font-family:sans-serif;}table{border-collapse:collapse;margin-bottom:32px;width:100%;}th,td{border:1px solid #ccc;padding:6px 10px;}th{background:#eee;}tr.transacao{background:#f9f9f9;font-weight:bold;}tr.sugestao{background:#fff;}tr.tfidf{background:#e6f7ff;}tr.bert{background:#fffbe6;}caption{font-size:1.1em;font-weight:bold;margin-bottom:8px;}td.nome{word-break:break-all;white-space:normal;max-width:400px;}</style></head><body>')
    html.append('<h1>Resultado do Matching de Transações e Contas</h1>')

    for i, t in enumerate(transacoes):
        nome_completo = extrai_nome_completo(t)
        desc = t.get('description', '')
        valor = t.get('amount', '')
        cnpj_cpf = t.get('cnpj_cpf', t.get('cpf_cnpj', t.get('cnpj', t.get('cpf', ''))))
        consulta = f'{sanitize(nome_completo)} {sanitize(desc)}'.strip()
        if not consulta:
            continue
        consulta_vec = vectorizer.transform([consulta])
        sims = cosine_similarity(consulta_vec, contas_matrix)[0]
        top_idx = np.argsort(sims)[::-1][:TOP_N]
        bert_scores = [sim_bert.name_similarity_bert(consulta, cdesc) for cdesc in contas_desc]
        top_bert_idx = np.argsort(bert_scores)[::-1][:TOP_N]

        html.append('<table>')
        html.append(f'<caption>Transação: {t.get("id", "")}</caption>')
        html.append('<tr class="transacao"><th>Nome</th><th>Descrição</th><th>Valor</th><th>CNPJ/CPF</th></tr>')
        html.append(f'<tr class="transacao"><td class="nome">{nome_completo}</td><td>{desc}</td><td>{valor}</td><td>{cnpj_cpf}</td></tr>')
        html.append('<tr class="sugestao"><th colspan="4">Conta Sugerida (TF-IDF)</th></tr>')
        for idx in top_idx:
            conta = contas[idx]
            score = sims[idx]
            html.append(f'<tr class="sugestao tfidf"><td colspan="4"><b>{conta["description"]}</b> <span style="color:#888;">({conta["classification"]})</span> <span style="float:right;">Score: {score:.2f}</span></td></tr>')
        html.append('<tr class="sugestao"><th colspan="4">Conta Sugerida (BERT)</th></tr>')
        for idx in top_bert_idx:
            conta = contas[idx]
            score = bert_scores[idx]
            html.append(f'<tr class="sugestao bert"><td colspan="4"><b>{conta["description"]}</b> <span style="color:#888;">({conta["classification"]})</span> <span style="float:right;">Score: {score:.2f}</span></td></tr>')
        html.append('</table>')
    html.append('</body></html>')

    with open('resultado_match.html', 'w', encoding='utf-8') as f:
        f.write('\n'.join(html))

if __name__ == '__main__':
    main() 