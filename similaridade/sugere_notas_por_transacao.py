import asyncio
from typing import List, Any

import requests
import json
import httpx

from invoice_suggestion import sugerir_notas_para_transacoes


async def request_transactions():
    url = "https://conciliador-api.bhub.ai/reconciliation/draft"

    payload = json.dumps({
        "cockpit_customer_id": "6b743d9b-6b6d-408c-9184-dab574eeab42",
        "competence": "2025-05",
        "bank_account_guids": [
            # "3ffd3321-e07a-11ee-ba2b-0a7ea7812cad",
            # "40145d2b-e07a-11ee-ba2b-0a7ea7812cad",
            # "4029d960-e07a-11ee-ba2b-0a7ea7812cad",
            # "********-e07a-11ee-ba2b-0a7ea7812cad",
            "40574e06-e07a-11ee-ba2b-0a7ea7812cad",
            # "406d9ca2-e07a-11ee-ba2b-0a7ea7812cad",
            # "6770efb4-9971-4041-b6d8-2da9fbd44018",
            # "72aeeff8-6304-4c27-b454-062c9dc83c2a",
            # "7426ff7c-4728-46a2-a4bd-4866252ce468",
            # "c649d79c-22f0-47fd-995c-c3a67050a0f0"
        ]
    })
    headers = {
        'Authorization': 'Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6ImppOGZnbllyQjZ4MDNOWnFCWU1UeCJ9.************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.PX7mqWoeg-d50f1YpvsOBtE9kTCLqxXA7ubM-m4jIS__i599HS458XsvF1V81XXGoeaOu233X2mUo55POAxdFgGkM51JvlEDs6xQDFe_jkuopo03K0PPBqZHm3Efab10Ah-9dGlffOqXQXkKExQhgiK4NT-l3nsYEV2anUwhcmI51NNCC0T6K1jhb49b2GmzwQWnUisS8OyKq3kP03uXpnJGUMvjFuZLEDvSLPLrKBeb5teyR4a7-VGwJmeLgmEgFQ32tR8fjUoUHoyEGDQ0oD-heRLhk_-8-4VAlbpXyifWWg5m2ct738_VgLEMKCqFzNiYzdGrUIjZSbl1vxR6TA',
        'Content-Type': 'application/json'
    }

    response = requests.request("POST", url, headers=headers, data=payload)
    return response.json()


async def request_invoices():
        url = "https://integradornf.bhub.ai/api/v1/invoices"
        params = {
            "taker_document": "42283979000100", # tomador da nota, emissor é o issuer_document (que é a bhub)
            # "issuer_document": "42283979000100",
            "start_date": "2025-03-01",
            "end_date": "2025-05-23",
            "offset": "0",
            # "type": "NFSE" # testar sem especificar o tipo pra ver se retorna todos os tipos de notas, a nota fiscal eletronica não está sendo retornada
        }
        headers = {
            "Authorization": "Bearer ********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"  #noqa
        }
        # with httpx.Client() as client:
        #     response = client.get(url, headers=headers, params=params)
        #     return response.json()
        async with httpx.AsyncClient() as client:
            response = await client.get(url, headers=headers, params=params)
            return response.json()


def extract_transactions(dados: dict) -> List[dict]:
    return dados.get("transactions", [])


def extract_invoices(dados: dict) -> List[dict]:
    return dados.get("data", [])


def extract_invoice_amount(nota: dict) -> float:
    net = nota.get("netAmount")
    if net is not None:
        try:
            return float(net)
        except Exception:
            pass
    amt = nota.get("amount")
    if amt is not None:
        try:
            return float(amt)
        except Exception:
            pass
    return 0.0


async def main():
    transacoes_raw = request_transactions()
    notas_raw = await request_invoices()
    transacoes = extract_transactions(transacoes_raw)
    notas = extract_invoices(notas_raw)

    resultados = sugerir_notas_para_transacoes(transacoes, notas)
    for resultado in resultados:
        t = resultado['transacao']
        sugestoes = resultado['sugestoes']
        print(
            f"\nTransação: {t.get('id')} | Valor: {t.get('amount')} | Desc: {t.get('description')}"
        )
        if not sugestoes:
            print("  Nenhuma nota sugerida.")
        else:
            for nota, score in sugestoes:
                print(
                    f"  Nota: {nota.get('id')} | Valor: {nota.get('amount')} | Nome: {nota.get('issuerName') or nota.get('takerName')} | Score: {score:.2f}"
                )


if __name__ == "__main__":
    asyncio.run(main())
