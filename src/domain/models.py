import enum

from sqlalchemy import (
    JSON,
    UUID,
    Boolean,
    Column,
    DateTime,
    Enum,
    ForeignKey,
    Index,
    Integer,
    Numeric,
    String,
    Text,
)
from sqlalchemy.ext.hybrid import hybrid_property
from sqlalchemy.orm import foreign, relationship, remote  # noqa
from sqlalchemy.sql import func

from infrastructure.database import DatabaseBaseModel


class ReconciliationStatusEnum(str, enum.Enum):
    DRAFT = "DRAFT"
    ACTIVE = "ACTIVE"
    FINISHED = "FINISHED"
    CANCELLED = "CANCELLED"


class TransactionTypeEnum(str, enum.Enum):
    INCOME = "INCOME"
    EXPENSE = "EXPENSE"


class TransactionStatusEnum(str, enum.Enum):
    NEW = "NEW"
    RECONCILED = "RECONCILED"
    ADVANCE_RECONCILED = "ADVANCE_RECONCILED"


class AccountConfigurationTypeEnum(str, enum.Enum):
    CHART_OF_ACCOUNTS_CODE = "CHART_OF_ACCOUNTS_CODE"


class SuggestionSource(str, enum.Enum):
    BINDER = "BINDER"


class BankAccountStatus(str, enum.Enum):
    """Account without operator feedback for 'Reconcile without Movement'
    or 'Finish without Statement'"""

    NO_OPERATOR_FEEDBACK = "NO_OPERATOR_FEEDBACK"

    """Statement in import process"""
    EXTRACT_IMPORTING = "EXTRACT_IMPORTING"

    """Statement being processed by Tio Patinhas and Binder"""
    EXTRACT_PROCESSING = "EXTRACT_PROCESSING"

    """Statement was not processed successfully by Tio Patinhas"""
    EXTRACT_PROCESSING_ERROR = "EXTRACT_PROCESSING_ERROR"

    """Statement already imported (processed by Tio Patinhas and Binder)"""
    EXTRACT_IMPORTED = "EXTRACT_IMPORTED"

    """Statement is waiting for Binder"""
    PENDING_SUGGESTIONS = "PENDING_SUGGESTIONS"

    """When the operator clicked 'Finish without Statement' button.
    In this case, BHub didn't receive the customer's statement"""
    FINISH_WITHOUT_EXTRACT = "FINISH_WITHOUT_EXTRACT"

    """When the operator clicked 'Finish without Transactions' button."""
    FINISH_WITHOUT_TRANSACTIONS = "FINISH_WITHOUT_TRANSACTIONS"


class Reconciliation(DatabaseBaseModel):
    """Reconciliation table"""

    competence = Column(String(255))
    status = Column(Enum(ReconciliationStatusEnum))
    cockpit_customer_id = Column(String(255))
    customer_cnpj = Column(String(255))
    operator = Column(String(255))
    orchestrate_process_id = Column(String(255))
    bank_account_guids = Column(JSON)
    status_bank_account_guids = Column(JSON)
    account_configurations = relationship(
        "AccountConfiguration",
        back_populates="reconciliation",
        cascade="all, delete-orphan",
    )
    journal_headers = relationship(
        "JournalHeader",
        back_populates="reconciliation",
        primaryjoin=(
            "and_(Reconciliation.id == foreign(JournalHeader.reconciliation_id), "
            "JournalHeader.deleted == False)"
        ),
        order_by="[JournalHeader.date, JournalHeader.amount.desc()]",
        cascade="all, delete-orphan",
    )
    file_uploads = relationship(
        "ReconciliationFileUpload",
        back_populates="reconciliation",
        primaryjoin=(
            "and_(Reconciliation.id == foreign(ReconciliationFileUpload.reconciliation_id), "  # noqa
            "ReconciliationFileUpload.deleted_at.is_(None))"
        ),
        order_by="desc(ReconciliationFileUpload.uploaded_at)",
        lazy="dynamic",
    )

    __table_args__ = (
        Index("ix_reconciliation_customer_cnpj", "customer_cnpj"),
        Index("ix_reconciliation_cockpit_customer_id", "cockpit_customer_id"),
        Index("ix_reconciliation_competence", "competence"),
    )

    @hybrid_property
    def bank_account_count(self):
        """
        Group transactions by cockpit_account_id (bank account), then calculates the
        total of transactions by bank account and the total with status RECONCILED.

        Returns:
            Dict[str, Dict[str, int]]:
                - The key is cockpit_account_id.
                - The value is a Dict with keys: total_transactions, total_reconciled.
        """
        grouped_transactions = {}

        for journal in self.journal_headers:
            grouped_transactions.setdefault(journal.cockpit_account_id, []).append(
                journal
            )

        accounts = self.bank_account_guids if self.bank_account_guids else []
        default_values = {item: {"total": 0, "reconciled": 0} for item in accounts}

        current_values = {
            cockpit_account_id: {
                "total": len(tx_list),
                "reconciled": sum(
                    1 for tx in tx_list if tx.status == TransactionStatusEnum.RECONCILED
                ),
            }
            for cockpit_account_id, tx_list in grouped_transactions.items()
        }

        return default_values | current_values

    @hybrid_property
    def latest_uploads_by_account(self):
        """
        Returns a dictionary mapping each cockpit account to its most recent upload
        file info.
        Format: {cockpit_account_id: {'status': str, 'upload_id': str}}
        Only includes the newest non-deleted upload per account.
        """
        if not hasattr(self, "_latest_uploads_cache"):
            uploads_by_account = {}

            for upload in self.file_uploads:
                if upload.cockpit_account_id not in uploads_by_account:
                    uploads_by_account[upload.cockpit_account_id] = {
                        "status": upload.status,
                        "upload_id": upload.upload_id,
                        "error_message": upload.error_message,
                        "metadata": upload._metadata,
                    }

            self._latest_uploads_cache = uploads_by_account

        return self._latest_uploads_cache


class FileUploadStatus(str, enum.Enum):
    """Status of file uploads in the reconciliation process"""

    """File has been uploaded but not yet processed"""
    UPLOADED = "UPLOADED"

    """File sheet is ready to be validate"""
    SHEET_READY_TO_VALIDATE = "SHEET_READY_TO_VALIDATE"

    """File is currently being processed"""
    PROCESSING = "PROCESSING"

    """File was successfully processed by tiopatinhas and Binder
    (even forced after 10 minutes)"""
    SHEET_PROCESSED = "SHEET_PROCESSED"

    """File processing failed"""
    FAILED = "FAILED"

    """File upload was cancelled"""
    CANCELLED = "CANCELLED"


class ReconciliationFileUpload(DatabaseBaseModel):
    upload_id = Column(String(255), nullable=True, unique=True)
    reconciliation_id = Column(
        UUID(as_uuid=True), ForeignKey("reconciliation.id"), nullable=False, index=True
    )
    reconciliation = relationship(
        "Reconciliation",
        back_populates="file_uploads",
        primaryjoin=(
            "and_(ReconciliationFileUpload.reconciliation_id == Reconciliation.id, "
            "ReconciliationFileUpload.deleted_at.is_(None))"
        ),
    )
    cockpit_account_id = Column(String(255), nullable=False, index=True)
    file_type = Column(String(255), nullable=True)
    status = Column(
        Enum(FileUploadStatus, name="file_upload_status"),
        default=FileUploadStatus.UPLOADED,
        nullable=False,
    )
    error_message = Column(Text, nullable=True)
    _metadata = Column(JSON, nullable=True)
    uploaded_at = Column(DateTime(timezone=True), server_default=func.now())
    processed_at = Column(DateTime(timezone=True), nullable=True)
    uploaded_by = Column(String(255), nullable=True)

    __table_args__ = (
        Index(
            "ix_reconciliation_file_upload_reconciliation_account",
            "reconciliation_id",
            "cockpit_account_id",
        ),
        Index("ix_reconciliation_file_upload_status", "status"),
    )


class PartnerEntity(DatabaseBaseModel):
    """Person table"""

    cpf_cnpj = Column(String(255))
    name = Column(String(255))
    description = Column(String(255))

    __table_args__ = (
        Index("partner_entity_cpf_cnpj", "cpf_cnpj"),
        Index("partner_entity_cpf_cnpj_name", "cpf_cnpj", "name"),
    )


class SuggestionTypeEnum(enum.Enum):
    ACCOUNT = "ACCOUNT"
    INVOICE = "INVOICE"
    INFO = "INFO"


class InvoiceMotiveEnum(str, enum.Enum):
    RENT_CONDOMINIUM = "aluguel_condominio"
    FINANCIAL_APPLICATION = "aplicacao_financeira"
    CAPITAL_INCREASE = "aumento_capital"
    CREDIT_CARD = "cartao_credito"
    EXCHANGE_CONTRACT = "contrato_cambio"
    EXPENSE_NO_INVOICE = "despesa_sem_nf"
    REFUND = "devolucao"
    PROFIT_DISTRIBUTION = "distribuicao_lucro"
    LOAN = "emprestimo"
    CONSUMPTION_INVOICE = "fatura_consumo"
    TAX = "imposto_taxa"
    INVOICE = "invoice"
    INTEREST_FINE = "juros_multa"
    MUTUAL = "mutuo"
    MISSING_INVOICE = "nf_nao_capturada"
    PAYMENT_TAXES = "pagamento_de_tributos"
    PRO_LABORE = "pro_labore"
    FINANCIAL_INCOME = "receita_financeira"
    TAX_REFUND = "restituicao_imposto"
    SALARY = "salario"
    BANK_FEE = "tarifa_bancaria"
    ACCOUNT_TRANSFER = "transferencia_contas"
    OTHERS = "outros"

    @classmethod
    def from_display_value(cls, display_value: str) -> str:
        """Convert display value to enum value"""
        mapping = {
            "Aluguel / Condomínio": cls.RENT_CONDOMINIUM.value,
            "Aplicação financeira / Resgate": cls.FINANCIAL_APPLICATION.value,
            "Aumento de capital social / AFAC": cls.CAPITAL_INCREASE.value,
            "Cartão de crédito": cls.CREDIT_CARD.value,
            "Contrato de câmbio": cls.EXCHANGE_CONTRACT.value,
            "Despesa sem NF / Reembolso": cls.EXPENSE_NO_INVOICE.value,
            "Devolução": cls.REFUND.value,
            "Distribuição de lucro": cls.PROFIT_DISTRIBUTION.value,
            "Empréstimo": cls.LOAN.value,
            "Fatura de consumo": cls.CONSUMPTION_INVOICE.value,
            "Imposto / Taxa / Contribuição": cls.TAX.value,
            "Invoice": cls.INVOICE.value,
            "Juros / Multa": cls.INTEREST_FINE.value,
            "Mútuo": cls.MUTUAL.value,
            "NF não capturada": cls.MISSING_INVOICE.value,
            "Pró-labore": cls.PRO_LABORE.value,
            "Pagamento de Tributos": cls.PAYMENT_TAXES.value,
            "Receita financeiras": cls.FINANCIAL_INCOME.value,
            "Restituição imposto / Taxa / Contribuição": cls.TAX_REFUND.value,
            "Salário / Estagiário / RPA": cls.SALARY.value,
            "Tarifa bancária / Despesa financeira": cls.BANK_FEE.value,
            "Transferência entre contas": cls.ACCOUNT_TRANSFER.value,
            "Outros": cls.OTHERS.value,
        }
        return mapping.get(display_value, display_value)


class SuggestedInvoice(DatabaseBaseModel):
    """Suggested Invoices table"""

    suggestion_id = Column(String(255))
    cockpit_account_id = Column(String(255))
    platform_transaction_id = Column(String(255))
    platform_invoice_id = Column(String(255))
    ranking = Column(Numeric(15, 2))
    exact_match = Column(Boolean())
    should_not_be_matched = Column(Boolean)
    version = Column(String(255))
    invoice_date = Column(String(255))
    invoice_company_name = Column(String(255))
    invoice_value = Column(String(255))
    invoice_number = Column(String(255))
    source = Column(
        Enum(SuggestionSource, name="suggestion_source_enum"), nullable=True
    )
    account_data = Column(JSON, nullable=True)
    suggestion_type = Column(Enum(SuggestionTypeEnum), nullable=True)

    __table_args__ = (
        Index(
            "suggested_invoice_transaction_invoice_index",
            "platform_transaction_id",
            "platform_invoice_id",
        ),
        Index(
            "suggested_invoice_transaction_should_not_be_matched",
            "platform_transaction_id",
            "should_not_be_matched",
        ),
    )


class AccountConfiguration(DatabaseBaseModel):
    """Account Configuration table"""

    cockpit_account_id = Column(String(255), index=True)
    type = Column(Enum(AccountConfigurationTypeEnum))
    value = Column(String(255))
    reconciliation_id = Column(
        UUID(as_uuid=True), ForeignKey("reconciliation.id"), nullable=False
    )
    reconciliation = relationship(
        "Reconciliation", back_populates="account_configurations"
    )
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(
        DateTime(timezone=True), default=func.now(), onupdate=func.now()
    )
    deleted_at = Column(DateTime(timezone=True), nullable=True)


class LinkedInvoice(DatabaseBaseModel):
    """Linked Invoices table"""

    platform_invoice_id = Column(String(255))
    link_date = Column(DateTime)
    link_percentual = Column(Numeric)
    invoice_number = Column(String(255))
    file_path = Column(String(255))
    journal_line_id = Column(
        UUID(as_uuid=True), ForeignKey("journal_line.id"), nullable=True
    )
    journal_line = relationship("JournalLine", back_populates="linked_invoices")


class JournalHeaderStatusEnum(str, enum.Enum):
    NEW = "NEW"
    RECONCILED = "RECONCILED"
    ADVANCE_RECONCILED = "ADVANCE_RECONCILED"


class JournalSourceTypeEnum(str, enum.Enum):
    BANK_TRANSACTION = "BANK_TRANSACTION"
    PAYROLL = "PAYROLL"
    GATEWAY = "GATEWAY"
    INVOICES = "INVOICES"


class JournalHeader(DatabaseBaseModel):
    """Journal Header table"""

    amount = Column(Numeric(precision=10, scale=2))
    date = Column(DateTime)
    reconciliation_id = Column(
        UUID(as_uuid=True), ForeignKey("reconciliation.id"), nullable=False
    )
    reconciliation = relationship(
        "Reconciliation", back_populates="journal_headers", lazy="joined"
    )
    description = Column(String(255))
    notes = Column(String(255))
    status = Column(
        Enum(JournalHeaderStatusEnum, name="journal_header_status"),
        nullable=True,
    )
    partner_entity_id = Column(UUID(as_uuid=True), ForeignKey("partner_entity.id"))
    partner_entity = relationship(
        "PartnerEntity",
        primaryjoin=partner_entity_id == PartnerEntity.id,
        lazy="joined",
    )
    journal_lines = relationship(
        "JournalLine",
        back_populates="journal_header",
        lazy="joined",
        primaryjoin=(
            "and_(JournalHeader.id == foreign(JournalLine.journal_header_id), "
            "JournalLine.deleted == False)"
        ),
        order_by="JournalLine.created_at",
    )
    source_type = Column(
        Enum("BANK_TRANSACTION", "PAYROLL", "GATEWAY", "INVOICES", name="source_type"),
        nullable=True,
    )
    source_reference_id = Column(String(255))
    cockpit_account_id = Column(String(255))
    _metadata = Column(
        JSON,
        comment="This JSON should contain aditional informations about the Header",
    )
    suggested_invoices = relationship(
        "SuggestedInvoice",
        primaryjoin=(
            "and_(JournalHeader.source_reference_id == "
            "foreign(SuggestedInvoice.platform_transaction_id), "
            "SuggestedInvoice.suggestion_type == 'INVOICE')"
        ),
        order_by="SuggestedInvoice.ranking",
        lazy="joined",
        overlaps="suggested_invoices",
    )

    @hybrid_property
    def type(self):
        return (
            TransactionTypeEnum.INCOME
            if self.amount > 0
            else TransactionTypeEnum.EXPENSE
        )

    @hybrid_property
    def debit_credit_journal_lines(self):
        """
        Retrieve the debit and credit lines from a given journal header.
        This function assumes that each `journal_header` should contain
        exactly one debit
        line and one credit line. If there are multiple debit or credit lines, or if any
        of them are missing, an exception is raised.

        Note:
            These constraints are temporary and will be adjusted in the future to
            support multiple entries in double-entry accounting.

        Args:
            journal_header (JournalHeader): The journal header containing the
            associated journal lines.

        Returns:
            Tuple[JournalLine, JournalLine]: A tuple containing the debit line and
            credit line.

        Raises:
            ValueError: If there is more than one debit or credit line.
            ValueError: If either the debit or credit line is missing.
        """
        debit_line = None
        credit_line = None

        for line in self.journal_lines:
            if line.line_type == OperationTypeEnum.DEBIT:
                if debit_line is not None:
                    raise ValueError(
                        f"Error: More than one debit line found for journal_header {self.id}"  # noqa
                    )
                debit_line = line
            elif line.line_type == OperationTypeEnum.CREDIT:
                if credit_line is not None:
                    raise ValueError(
                        f"Error: More than one credit line found for journal_header {self.id}"  # noqa
                    )
                credit_line = line

        if debit_line is None or credit_line is None:
            raise ValueError(
                f"Error: journal_header {self.id} must contain exactly one debit and one credit line"  # noqa
            )

        return debit_line, credit_line


class AccountTypeEnum(str, enum.Enum):
    ANALYTIC = "ANALYTIC"
    SYNTHETIC = "SYNTHETIC"


class AccountStatus(str, enum.Enum):
    ACTIVE = "ACTIVE"
    INACTIVE = "INACTIVE"


class OperationTypeEnum(str, enum.Enum):
    DEBIT = "DEBIT"
    CREDIT = "CREDIT"

    @classmethod
    def to_display_value(cls, operation_type: str) -> str:
        """Convert enum value to display value"""
        if operation_type == OperationTypeEnum.DEBIT:
            return "Devedora"
        elif operation_type == OperationTypeEnum.CREDIT:
            return "Credora"
        return operation_type


class Account(DatabaseBaseModel):
    cnpj = Column(String(30))
    parent_code = Column(String(10), nullable=True)
    description = Column(String(512))
    classification = Column(String(20))
    level = Column(Integer)
    short_code = Column(Integer)
    status = Column(
        Enum(AccountStatus, name="account_status_enum"),
    )
    account_type = Column(
        Enum(AccountTypeEnum, name="account_type_enum"),
    )
    operation_type = Column(
        Enum(OperationTypeEnum, name="operation_type_enum"),
    )


class JournalLine(DatabaseBaseModel):
    """Journal Line table"""

    line_type = Column(
        Enum(OperationTypeEnum, name="journal_line_type"),
    )
    amount = Column(Numeric(precision=10, scale=2))
    description = Column(String(255), nullable=True)
    notes = Column(String(255), nullable=True)
    account_id = Column(String(255), nullable=True)
    account_chart_id = Column(
        UUID(as_uuid=True), ForeignKey("account.id"), nullable=True
    )
    account = relationship(
        "Account",
        primaryjoin=account_chart_id == Account.id,
        lazy="joined",
    )
    journal_header_id = Column(UUID(as_uuid=True), ForeignKey("journal_header.id"))
    journal_header = relationship(
        "JournalHeader",
        primaryjoin=journal_header_id == JournalHeader.id,
        lazy="joined",
    )
    _metadata = Column(
        JSON,
        comment="This JSON should contain aditional informations about the JournalLine",
    )
    linked_invoices = relationship(
        "LinkedInvoice",
        primaryjoin=(
            "and_(JournalLine.id == foreign(LinkedInvoice.journal_line_id), "
            "LinkedInvoice.deleted == False)"
        ),
        back_populates="journal_line",
        lazy="joined",
    )
    _suggested_account = relationship(
        "SuggestedInvoice",
        primaryjoin=(
            "and_("
            "foreign(JournalLine.journal_header_id) == remote(JournalHeader.id), "
            "remote(JournalHeader.source_reference_id) == foreign(SuggestedInvoice.platform_transaction_id), "  # noqa
            "SuggestedInvoice.suggestion_type == 'ACCOUNT', "
            "exists().where("
            "and_("
            "JournalHeader.reconciliation_id == Reconciliation.id, "
            ")"
            ")"
            ")"
        ),
        lazy="select",
        uselist=False,
        viewonly=True,
    )

    @hybrid_property
    def suggested_account(self):
        if self._suggested_account:
            transaction_type = self.journal_header.type
            line_type = self.line_type

            should_receive_suggestion = (
                transaction_type == TransactionTypeEnum.EXPENSE
                and line_type == OperationTypeEnum.DEBIT
            ) or (
                transaction_type == TransactionTypeEnum.INCOME
                and line_type == OperationTypeEnum.CREDIT
            )

            if not should_receive_suggestion:
                return None

            data = self._suggested_account.account_data
            data["account_type"] = (
                "expense_account"
                if self.journal_header.type == TransactionTypeEnum.EXPENSE
                else "income_account"
            )

            account_value = None
            if self.account_id is None and self.account_chart_id is not None:
                account = self.account
                account_value = account.short_code if account is not None else None
            else:
                account_value = self.account_id

            data["status"] = (
                "NEW"
                if account_value is None
                else "ACCEPTED" if account_value == str(data["number"]) else "REJECTED"
            )

            data["account_chart_id"] = str(self.account_chart_id)

            return data
        return None

    def determine_status(self) -> TransactionStatusEnum:
        return (
            TransactionStatusEnum.RECONCILED
            if self.account_id
            else TransactionStatusEnum.NEW
        )


class ActivityLog(DatabaseBaseModel):

    log_name = Column(String, nullable=True, index=True)
    description = Column(Text, nullable=False)
    subject_id = Column(String, nullable=True)
    subject_type = Column(String, nullable=True)
    causer_id = Column(String, nullable=True)
    causer_type = Column(String, nullable=True)
    properties = Column(JSON, nullable=True)
    event = Column(String, nullable=True)


JournalLine.setup_listeners()
JournalHeader.setup_listeners()
Reconciliation.setup_listeners()
ReconciliationFileUpload.setup_listeners()
